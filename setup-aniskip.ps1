# AniSkip Setup Script for Windows
# Run this in PowerShell as Administrator

Write-Host "=== AniSkip Setup for MPV ===" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Warning: Not running as administrator. Some operations may fail." -ForegroundColor Yellow
    Write-Host ""
}

# Get MPV config directory
$mpvConfig = "$env:APPDATA\mpv"
$scriptsDir = "$mpvConfig\scripts"
$scriptOptsDir = "$mpvConfig\script-opts"

Write-Host "MPV Config Directory: $mpvConfig" -ForegroundColor Green

# Create directories if they don't exist
if (-not (Test-Path $mpvConfig)) {
    Write-Host "Creating MPV config directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $mpvConfig -Force | Out-Null
}

if (-not (Test-Path $scriptsDir)) {
    Write-Host "Creating scripts directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $scriptsDir -Force | Out-Null
}

if (-not (Test-Path $scriptOptsDir)) {
    Write-Host "Creating script-opts directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $scriptOptsDir -Force | Out-Null
}

# Check for curl
Write-Host "Checking for curl..." -ForegroundColor Yellow
try {
    $curlVersion = & curl --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ curl is available" -ForegroundColor Green
    } else {
        throw "curl not found"
    }
} catch {
    Write-Host "✗ curl not found" -ForegroundColor Red
    Write-Host "Attempting to install curl..." -ForegroundColor Yellow
    
    try {
        # Try to add Windows curl capability
        if ($isAdmin) {
            Add-WindowsCapability -Online -Name "curl~~~~" -ErrorAction Stop
            Write-Host "✓ curl installed successfully" -ForegroundColor Green
        } else {
            Write-Host "Cannot install curl without administrator privileges." -ForegroundColor Red
            Write-Host "Please run as administrator or install curl manually." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Failed to install curl automatically." -ForegroundColor Red
        Write-Host "You can download curl from: https://curl.se/windows/" -ForegroundColor Yellow
    }
}

# Copy files
Write-Host "Installing AniSkip files..." -ForegroundColor Yellow

# Remove old files
$oldFiles = @(
    "$scriptsDir\aniskip_new.lua",
    "$scriptsDir\install-aniskip.bat"
)

foreach ($file in $oldFiles) {
    if (Test-Path $file) {
        Write-Host "Removing old file: $(Split-Path $file -Leaf)" -ForegroundColor Yellow
        Remove-Item $file -Force
    }
}

# Copy new files
try {
    Copy-Item "aniskip.lua" "$scriptsDir\aniskip.lua" -Force
    Write-Host "✓ Installed aniskip.lua" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to copy aniskip.lua: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    Copy-Item "aniskip.conf" "$scriptOptsDir\aniskip.conf" -Force
    Write-Host "✓ Installed aniskip.conf" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to copy aniskip.conf: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Installation Complete ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "Usage:" -ForegroundColor White
Write-Host "• The script automatically detects anime from filenames" -ForegroundColor Gray
Write-Host "• Press Ctrl+S to manually skip to next opening/ending" -ForegroundColor Gray
Write-Host "• Press Ctrl+I to show current anime detection info" -ForegroundColor Gray
Write-Host ""
Write-Host "Configuration: $scriptOptsDir\aniskip.conf" -ForegroundColor Gray
Write-Host ""
Write-Host "Test it by playing an anime file with MPV!" -ForegroundColor Green
Write-Host ""

# Test API connectivity
Write-Host "Testing API connectivity..." -ForegroundColor Yellow
try {
    $testUrl = "https://api.aniskip.com/v2/skip-times/test/1?types=op,ed"
    $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    Write-Host "✓ API is accessible" -ForegroundColor Green
} catch {
    Write-Host "✗ Cannot reach aniskip.com API" -ForegroundColor Red
    Write-Host "Check your internet connection" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
