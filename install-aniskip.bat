@echo off
echo Installing AniSkip for MPV...
echo.

REM Get MPV config directory
set "MPV_CONFIG=%APPDATA%\mpv"
if not exist "%MPV_CONFIG%" (
    echo Creating MPV config directory...
    mkdir "%MPV_CONFIG%"
)

REM Create scripts directory
if not exist "%MPV_CONFIG%\scripts" (
    echo Creating scripts directory...
    mkdir "%MPV_CONFIG%\scripts"
)

REM Create script-opts directory
if not exist "%MPV_CONFIG%\script-opts" (
    echo Creating script-opts directory...
    mkdir "%MPV_CONFIG%\script-opts"
)

REM Copy files
echo Copying AniSkip script...
copy "aniskip.lua" "%MPV_CONFIG%\scripts\" >nul
if errorlevel 1 (
    echo Error: Could not copy aniskip.lua
    pause
    exit /b 1
)

echo Copying configuration...
copy "aniskip.conf" "%MPV_CONFIG%\script-opts\" >nul
if errorlevel 1 (
    echo Error: Could not copy aniskip.conf
    pause
    exit /b 1
)

echo.
echo Installation complete!
echo.
echo AniSkip has been installed to: %MPV_CONFIG%
echo.
echo Usage:
echo - The script will automatically detect anime from filenames
echo - Press Ctrl+S to manually skip to next opening/ending
echo - Press Ctrl+I to show current anime info
echo.
echo Configuration file: %MPV_CONFIG%\script-opts\aniskip.conf
echo.
echo Note: Make sure you have curl installed and available in your PATH
echo for the script to fetch skip times from the aniskip.com API.
echo.
pause
