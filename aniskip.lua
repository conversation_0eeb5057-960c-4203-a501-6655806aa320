-- AniSkip - Automated anime opening/ending skipper using aniskip.com API
-- Based on https://github.com/aniskip/aniskip-cli

local mp = require('mp')
local utils = require('mp.utils')
local msg = require('mp.msg')

-- Configuration
local options = {
    api_url = "https://api.aniskip.com/v2/skip-times",
    skip_op = true,
    skip_ed = true,
    skip_mixed_op = true,
    skip_mixed_ed = true,
    skip_recap = false,
    categories = "op,ed",
    auto_detect = true,
    show_notifications = true,
    debug = false
}

-- Read user options
require('mp.options').read_options(options, "aniskip")

-- Global variables
local skip_times = {}
local current_anime = nil
local current_episode = nil
local skip_applied = {}

-- Utility functions
local function log(level, msg_text)
    if options.debug or level ~= "debug" then
        msg[level](msg_text)
    end
end

local function show_osd(text, duration)
    if options.show_notifications then
        mp.osd_message(text, duration or 3)
    end
end

-- Parse filename to extract anime info
local function parse_filename(filename)
    log("debug", "Parsing filename: " .. filename)
    
    -- Remove file extension
    local name = filename:match("(.+)%..+$") or filename
    
    -- Common patterns for anime episodes
    local patterns = {
        -- [Group] Anime Name - Episode (Year) [Quality]
        "%[.-%]%s*(.-)%s*[%-–]%s*[Ee]?[Pp]?%s*0*(%d+)",
        -- Anime Name - Episode
        "(.-)%s*[%-–]%s*[Ee]?[Pp]?%s*0*(%d+)",
        -- Anime Name Episode
        "(.-)%s+[Ee]?[Pp]?%s*0*(%d+)",
        -- Anime Name S01E01 format
        "(.-)%s*[Ss]%d+[Ee]0*(%d+)",
        -- Just episode number at the end
        "(.-)%s+0*(%d+)%s*$"
    }
    
    for _, pattern in ipairs(patterns) do
        local anime_name, episode = name:match(pattern)
        if anime_name and episode then
            -- Clean up anime name
            anime_name = anime_name:gsub("%[.-%]", ""):gsub("^%s+", ""):gsub("%s+$", "")
            episode = tonumber(episode)
            log("debug", "Extracted: '" .. anime_name .. "' episode " .. episode)
            return anime_name, episode
        end
    end
    
    log("debug", "Could not parse filename")
    return nil, nil
end

-- Make HTTP request to aniskip API
local function fetch_skip_times(anime_name, episode)
    log("info", "Fetching skip times for: " .. anime_name .. " episode " .. episode)

    -- URL encode the anime name
    local encoded_name = anime_name:gsub(" ", "%%20"):gsub("[^%w%-_.~%%]", function(c)
        return string.format("%%%02X", string.byte(c))
    end)

    local url = string.format("%s/%s/%d?types=%s",
        options.api_url, encoded_name, episode, options.categories)

    log("debug", "API URL: " .. url)

    local result = nil

    -- Try different methods based on platform
    -- Method 1: curl (cross-platform)
    local curl_args = {
        "curl", "-s", "-L", "--max-time", "10",
        "-H", "User-Agent: aniskip-mpv/1.0",
        url
    }

    result = utils.subprocess({
        name = "curl",
        args = curl_args,
        capture_stdout = true,
        capture_stderr = true
    })

    -- Method 2: PowerShell (Windows)
    if result.status ~= 0 then
        log("debug", "curl failed, trying PowerShell...")
        local ps_cmd = string.format(
            'powershell.exe -WindowStyle Hidden -Command "try { (Invoke-WebRequest -Uri \'%s\' -UserAgent \'aniskip-mpv/1.0\' -TimeoutSec 10 -UseBasicParsing).Content } catch { exit 1 }"',
            url
        )

        result = utils.subprocess({
            name = "cmd",
            args = {"/c", ps_cmd},
            capture_stdout = true,
            capture_stderr = true
        })
    end

    -- Method 3: Windows certutil + temp file (fallback)
    if result.status ~= 0 then
        log("debug", "PowerShell failed, trying certutil...")
        local temp_file = os.getenv("TEMP") .. "\\aniskip_" .. os.time() .. ".tmp"
        local certutil_cmd = string.format('certutil -urlcache -split -f "%s" "%s" >nul 2>&1 && type "%s" && del "%s"',
            url, temp_file, temp_file, temp_file)

        result = utils.subprocess({
            name = "cmd",
            args = {"/c", certutil_cmd},
            capture_stdout = true,
            capture_stderr = true
        })
    end

    if result.status ~= 0 then
        log("error", "All HTTP methods failed. Please install curl or ensure PowerShell is available.")
        show_osd("AniSkip: Cannot fetch data - install curl", 3)
        return nil
    end
    
    log("debug", "API response: " .. result.stdout)
    
    -- Parse JSON response (simple parser for our needs)
    local skip_data = {}
    if result.stdout and result.stdout ~= "" then
        -- Look for skip time objects in the response
        for skip_time in result.stdout:gmatch('"skipType":"([^"]+)".-"startTime":([%d%.]+).-"endTime":([%d%.]+)') do
            local skip_type, start_time, end_time = skip_time:match('([^"]+).-([%d%.]+).-([%d%.]+)')
            if skip_type and start_time and end_time then
                table.insert(skip_data, {
                    type = skip_type,
                    start_time = tonumber(start_time),
                    end_time = tonumber(end_time)
                })
            end
        end
        
        -- Alternative parsing method
        if #skip_data == 0 then
            for start_time, end_time, skip_type in result.stdout:gmatch('"startTime":([%d%.]+).-"endTime":([%d%.]+).-"skipType":"([^"]+)"') do
                table.insert(skip_data, {
                    type = skip_type,
                    start_time = tonumber(start_time),
                    end_time = tonumber(end_time)
                })
            end
        end
    end
    
    if #skip_data > 0 then
        log("info", "Found " .. #skip_data .. " skip times")
        for i, skip in ipairs(skip_data) do
            log("debug", string.format("Skip %d: %s from %.2f to %.2f", 
                i, skip.type, skip.start_time, skip.end_time))
        end
        return skip_data
    else
        log("info", "No skip times found for this episode")
        return nil
    end
end

-- Check if we should skip based on current time
local function check_skip()
    local current_time = mp.get_property_number("time-pos")
    if not current_time or #skip_times == 0 then
        return
    end
    
    for i, skip in ipairs(skip_times) do
        local skip_key = skip.type .. "_" .. skip.start_time
        
        if not skip_applied[skip_key] and 
           current_time >= skip.start_time and 
           current_time < skip.end_time then
            
            -- Check if we should skip this type
            local should_skip = false
            if skip.type == "op" and options.skip_op then
                should_skip = true
            elseif skip.type == "ed" and options.skip_ed then
                should_skip = true
            elseif skip.type == "mixed-op" and options.skip_mixed_op then
                should_skip = true
            elseif skip.type == "mixed-ed" and options.skip_mixed_ed then
                should_skip = true
            elseif skip.type == "recap" and options.skip_recap then
                should_skip = true
            end
            
            if should_skip then
                log("info", string.format("Skipping %s from %.2f to %.2f", 
                    skip.type, skip.start_time, skip.end_time))
                show_osd("Skipped " .. skip.type:upper(), 2)
                mp.set_property_number("time-pos", skip.end_time)
                skip_applied[skip_key] = true
            end
        end
    end
end

-- Handle file load
local function on_file_loaded()
    local filename = mp.get_property("filename")
    if not filename then
        return
    end
    
    log("debug", "File loaded: " .. filename)
    
    -- Reset state
    skip_times = {}
    skip_applied = {}
    current_anime = nil
    current_episode = nil
    
    if not options.auto_detect then
        log("debug", "Auto-detection disabled")
        return
    end
    
    -- Parse filename
    local anime_name, episode = parse_filename(filename)
    if not anime_name or not episode then
        log("info", "Could not detect anime from filename")
        return
    end
    
    current_anime = anime_name
    current_episode = episode
    
    -- Fetch skip times
    local fetched_times = fetch_skip_times(anime_name, episode)
    if fetched_times then
        skip_times = fetched_times
        show_osd("AniSkip: Loaded " .. #skip_times .. " skip times", 3)
    else
        show_osd("AniSkip: No skip data found", 2)
    end
end

-- Manual skip command
local function manual_skip()
    local current_time = mp.get_property_number("time-pos")
    if not current_time or #skip_times == 0 then
        show_osd("AniSkip: No skip data available", 2)
        return
    end
    
    -- Find next skip time
    local next_skip = nil
    for _, skip in ipairs(skip_times) do
        if current_time < skip.start_time then
            if not next_skip or skip.start_time < next_skip.start_time then
                next_skip = skip
            end
        end
    end
    
    if next_skip then
        log("info", string.format("Manual skip to %.2f (%s)", next_skip.end_time, next_skip.type))
        show_osd("Skipped " .. next_skip.type:upper(), 2)
        mp.set_property_number("time-pos", next_skip.end_time)
    else
        show_osd("AniSkip: No more skips available", 2)
    end
end

-- Event handlers
mp.register_event("file-loaded", on_file_loaded)
mp.observe_property("time-pos", "number", check_skip)

-- Key bindings
mp.add_key_binding("ctrl+s", "aniskip-manual", manual_skip)

-- Info command
mp.add_key_binding("ctrl+i", "aniskip-info", function()
    if current_anime and current_episode then
        local info = string.format("AniSkip: %s Episode %d\nSkip times: %d", 
            current_anime, current_episode, #skip_times)
        show_osd(info, 5)
    else
        show_osd("AniSkip: No anime detected", 2)
    end
end)

log("info", "AniSkip loaded - Press Ctrl+S for manual skip, Ctrl+I for info")
